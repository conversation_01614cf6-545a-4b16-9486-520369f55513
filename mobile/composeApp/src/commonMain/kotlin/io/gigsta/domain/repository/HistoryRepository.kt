package io.gigsta.domain.repository

import io.gigsta.domain.model.HistoryItem
import io.gigsta.domain.model.HistoryItemType

interface HistoryRepository {
    suspend fun getHistoryItems(type: HistoryItemType): List<HistoryItem>
    suspend fun createHistoryItem(item: HistoryItem): Result<HistoryItem>
    suspend fun updateHistoryItem(item: HistoryItem): Result<HistoryItem>
    suspend fun deleteHistoryItem(id: String): Result<Unit>
}
