package io.gigsta.data.repository

import io.gigsta.domain.model.HistoryItem
import io.gigsta.domain.model.HistoryItemType
import io.gigsta.domain.repository.HistoryRepository

class HistoryRepositoryImpl : HistoryRepository {
    
    // Dummy data for now - in real app this would come from database/API
    private val dummyData = mapOf(
        HistoryItemType.CV_BUILDER to listOf(
            HistoryItem("1", "UI/UX Designer", "Senior", 0.85f, "11 May 2024", HistoryItemType.CV_BUILDER),
            HistoryItem("2", "Graphic Designer", "Mid-level", 0.56f, "11 May 2024", HistoryItemType.CV_BUILDER),
            HistoryItem("3", "Product Designer", "Junior", 0.28f, "11 May 2024", HistoryItemType.CV_BUILDER),
            HistoryItem("4", "Designer", "Entry", 0.19f, "22 May 2024", HistoryItemType.CV_BUILDER)
        ),
        HistoryItemType.APPLICATION_LETTER to listOf(
            HistoryItem("5", "Google Application", "Software Engineer", 1.0f, "15 May 2024", HistoryItemType.APPLICATION_LETTER),
            HistoryItem("6", "Meta Cover Letter", "Product Manager", 0.75f, "12 May 2024", HistoryItemType.APPLICATION_LETTER),
            HistoryItem("7", "Apple Letter", "Designer", 0.45f, "10 May 2024", HistoryItemType.APPLICATION_LETTER)
        ),
        HistoryItemType.EMAIL_APPLICATION to listOf(
            HistoryItem("8", "Follow-up Email", "Google", 1.0f, "16 May 2024", HistoryItemType.EMAIL_APPLICATION),
            HistoryItem("9", "Thank You Email", "Meta", 1.0f, "13 May 2024", HistoryItemType.EMAIL_APPLICATION),
            HistoryItem("10", "Introduction Email", "Apple", 0.60f, "11 May 2024", HistoryItemType.EMAIL_APPLICATION)
        ),
        HistoryItemType.JOB_MATCH to listOf(
            HistoryItem("11", "Senior Developer", "95% match", 0.95f, "Today", HistoryItemType.JOB_MATCH),
            HistoryItem("12", "Product Manager", "87% match", 0.87f, "Yesterday", HistoryItemType.JOB_MATCH),
            HistoryItem("13", "UX Designer", "72% match", 0.72f, "2 days ago", HistoryItemType.JOB_MATCH)
        )
    )
    
    override suspend fun getHistoryItems(type: HistoryItemType): List<HistoryItem> {
        return dummyData[type] ?: emptyList()
    }
    
    override suspend fun createHistoryItem(item: HistoryItem): Result<HistoryItem> {
        // TODO: Implement actual creation logic
        return Result.success(item)
    }
    
    override suspend fun updateHistoryItem(item: HistoryItem): Result<HistoryItem> {
        // TODO: Implement actual update logic
        return Result.success(item)
    }
    
    override suspend fun deleteHistoryItem(id: String): Result<Unit> {
        // TODO: Implement actual deletion logic
        return Result.success(Unit)
    }
}
