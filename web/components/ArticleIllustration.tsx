interface ArticleIllustrationProps {
  type: 'lamaran-kerja' | 'cv-resume' | 'interview' | 'karir' | 'default';
  className?: string;
}

export default function ArticleIllustration({ type, className = "w-full h-full" }: ArticleIllustrationProps) {
  const getIllustration = () => {
    switch (type) {
      case 'lamaran-kerja':
        return (
          <svg viewBox="0 0 400 240" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background */}
            <rect width="400" height="240" fill="url(#gradient1)" />
            
            {/* Document */}
            <rect x="80" y="40" width="240" height="160" rx="8" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            
            {/* Header lines */}
            <rect x="100" y="60" width="120" height="8" rx="4" fill="#3B82F6" />
            <rect x="100" y="75" width="80" height="4" rx="2" fill="#9CA3AF" />
            <rect x="100" y="85" width="100" height="4" rx="2" fill="#9CA3AF" />
            
            {/* Content lines */}
            <rect x="100" y="110" width="200" height="4" rx="2" fill="#D1D5DB" />
            <rect x="100" y="120" width="180" height="4" rx="2" fill="#D1D5DB" />
            <rect x="100" y="130" width="160" height="4" rx="2" fill="#D1D5DB" />
            <rect x="100" y="140" width="190" height="4" rx="2" fill="#D1D5DB" />
            
            {/* Signature area */}
            <rect x="100" y="165" width="60" height="4" rx="2" fill="#10B981" />
            <rect x="100" y="175" width="80" height="6" rx="3" fill="#059669" />
            
            {/* Decorative elements */}
            <circle cx="340" cy="60" r="20" fill="#EF4444" opacity="0.1" />
            <circle cx="60" cy="180" r="15" fill="#8B5CF6" opacity="0.1" />
            
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#F3F4F6" />
                <stop offset="100%" stopColor="#E5E7EB" />
              </linearGradient>
            </defs>
          </svg>
        );
        
      case 'cv-resume':
        return (
          <svg viewBox="0 0 400 240" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background */}
            <rect width="400" height="240" fill="url(#gradient2)" />
            
            {/* CV Document */}
            <rect x="100" y="30" width="200" height="180" rx="8" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            
            {/* Profile section */}
            <circle cx="150" cy="70" r="20" fill="#3B82F6" />
            <rect x="180" y="60" width="80" height="6" rx="3" fill="#1F2937" />
            <rect x="180" y="72" width="60" height="4" rx="2" fill="#6B7280" />
            <rect x="180" y="82" width="70" height="4" rx="2" fill="#6B7280" />
            
            {/* Experience section */}
            <rect x="120" y="110" width="40" height="4" rx="2" fill="#8B5CF6" />
            <rect x="120" y="125" width="120" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="135" width="100" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="145" width="110" height="4" rx="2" fill="#D1D5DB" />
            
            {/* Skills section */}
            <rect x="120" y="170" width="30" height="4" rx="2" fill="#10B981" />
            <rect x="120" y="185" width="80" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="195" width="70" height="4" rx="2" fill="#D1D5DB" />
            
            {/* Decorative elements */}
            <rect x="320" y="50" width="60" height="80" rx="4" fill="#FEF3C7" opacity="0.5" />
            <rect x="330" y="60" width="40" height="4" rx="2" fill="#F59E0B" />
            <rect x="330" y="70" width="35" height="4" rx="2" fill="#F59E0B" />
            <rect x="330" y="80" width="30" height="4" rx="2" fill="#F59E0B" />
            
            <defs>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#EEF2FF" />
                <stop offset="100%" stopColor="#E0E7FF" />
              </linearGradient>
            </defs>
          </svg>
        );
        
      case 'interview':
        return (
          <svg viewBox="0 0 400 240" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background */}
            <rect width="400" height="240" fill="url(#gradient3)" />
            
            {/* Table */}
            <rect x="50" y="140" width="300" height="80" rx="8" fill="#8B5CF6" opacity="0.1" />
            
            {/* Person 1 (Interviewer) */}
            <circle cx="120" cy="120" r="25" fill="#3B82F6" />
            <rect x="95" y="145" width="50" height="60" rx="25" fill="#1E40AF" />
            
            {/* Person 2 (Candidate) */}
            <circle cx="280" cy="120" r="25" fill="#10B981" />
            <rect x="255" y="145" width="50" height="60" rx="25" fill="#059669" />
            
            {/* Speech bubbles */}
            <ellipse cx="150" cy="80" rx="40" ry="20" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            <polygon points="130,95 140,100 150,95" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            <rect x="125" y="72" width="50" height="4" rx="2" fill="#6B7280" />
            <rect x="125" y="80" width="40" height="4" rx="2" fill="#6B7280" />
            
            <ellipse cx="250" cy="80" rx="40" ry="20" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            <polygon points="270,95 260,100 250,95" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            <rect x="225" y="72" width="50" height="4" rx="2" fill="#6B7280" />
            <rect x="225" y="80" width="35" height="4" rx="2" fill="#6B7280" />
            
            {/* Decorative elements */}
            <circle cx="350" cy="50" r="12" fill="#F59E0B" opacity="0.3" />
            <circle cx="50" cy="50" r="8" fill="#EF4444" opacity="0.3" />
            
            <defs>
              <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#F0FDF4" />
                <stop offset="100%" stopColor="#DCFCE7" />
              </linearGradient>
            </defs>
          </svg>
        );
        
      case 'karir':
        return (
          <svg viewBox="0 0 400 240" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background */}
            <rect width="400" height="240" fill="url(#gradient4)" />
            
            {/* Career ladder/steps */}
            <rect x="80" y="180" width="60" height="20" rx="4" fill="#3B82F6" />
            <rect x="160" y="150" width="60" height="20" rx="4" fill="#8B5CF6" />
            <rect x="240" y="120" width="60" height="20" rx="4" fill="#10B981" />
            <rect x="320" y="90" width="60" height="20" rx="4" fill="#F59E0B" />
            
            {/* Connecting lines */}
            <path d="M140 190 L160 160" stroke="#6B7280" strokeWidth="2" strokeDasharray="5,5" />
            <path d="M220 160 L240 130" stroke="#6B7280" strokeWidth="2" strokeDasharray="5,5" />
            <path d="M300 130 L320 100" stroke="#6B7280" strokeWidth="2" strokeDasharray="5,5" />
            
            {/* Person climbing */}
            <circle cx="200" cy="140" r="15" fill="#1F2937" />
            <rect x="190" y="155" width="20" height="30" rx="10" fill="#374151" />
            
            {/* Success elements */}
            <circle cx="350" cy="60" r="20" fill="#FEF3C7" />
            <polygon points="350,50 355,60 350,70 345,60" fill="#F59E0B" />
            
            {/* Growth arrow */}
            <path d="M50 200 L50 50" stroke="#10B981" strokeWidth="3" markerEnd="url(#arrowhead)" />
            <polygon points="45,60 50,50 55,60" fill="#10B981" />
            
            <defs>
              <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#FFFBEB" />
                <stop offset="100%" stopColor="#FEF3C7" />
              </linearGradient>
            </defs>
          </svg>
        );
        
      default:
        return (
          <svg viewBox="0 0 400 240" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background */}
            <rect width="400" height="240" fill="url(#gradient5)" />
            
            {/* Generic document/article illustration */}
            <rect x="100" y="60" width="200" height="120" rx="8" fill="white" stroke="#E5E7EB" strokeWidth="2" />
            
            {/* Title */}
            <rect x="120" y="80" width="100" height="8" rx="4" fill="#1F2937" />
            
            {/* Content lines */}
            <rect x="120" y="100" width="160" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="110" width="140" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="120" width="150" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="130" width="130" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="140" width="145" height="4" rx="2" fill="#D1D5DB" />
            <rect x="120" y="150" width="120" height="4" rx="2" fill="#D1D5DB" />
            
            {/* Decorative elements */}
            <circle cx="50" cy="50" r="15" fill="#3B82F6" opacity="0.2" />
            <circle cx="350" cy="190" r="20" fill="#10B981" opacity="0.2" />
            
            <defs>
              <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#F9FAFB" />
                <stop offset="100%" stopColor="#F3F4F6" />
              </linearGradient>
            </defs>
          </svg>
        );
    }
  };

  return getIllustration();
}
