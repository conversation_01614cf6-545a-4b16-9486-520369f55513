import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import './globals.css';
import { AnalyticsProvider } from '../components/AnalyticsProvider';
import { RollbarErrorBoundary } from '../components/RollbarErrorBoundary';
import GoogleAdsense from '../components/GoogleAdsense';
import { GoogleTagManager } from '@next/third-parties/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Buat CV Online untuk Melamar Kerja dengan bantuan AI',
  description: 'Buat CV ATS dan surat lamaran profesional secara otomatis dengan AI. Platform karir terlengkap untuk meningkatkan peluang kerja Anda hingga 3x lipat.',
  keywords: 'CV ATS friendly, buat CV online, surat lamaran AI, email lamaran kerja, analisis peker<PERSON>, resume builder Indonesia, AI untuk karir, template CV profesional, buat CV otomatis, surat lamaran otomatis',
  openGraph: {
    title: 'Buat CV dan Surat Lamaran Kerja dengan AI',
    description: 'Buat CV ATS-friendly, surat lamaran profesional, email lamaran, dan analisis kecocokan pekerjaan dengan AI. Tingkatkan peluang kerja hingga 3x lipat!',
    url: 'https://gigsta.io',
    siteName: 'Gigsta',
    type: 'website',
    locale: 'id_ID',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id">
      <head>
        <meta name="google-adsense-account" content={`${process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID}`} />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        {/* Meta Pixel Code */}
        <Script
          id="meta-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '****************');
              fbq('track', 'PageView');
            `,
          }}
        />
        <noscript>
          <img
            height="1"
            width="1"
            style={{display: 'none'}}
            src="https://www.facebook.com/tr?id=****************&ev=PageView&noscript=1"
            alt=""
          />
        </noscript>
        <GoogleTagManager gtmId='GTM-N375ZS5H' />
        <GoogleAdsense />
        <RollbarErrorBoundary>
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </RollbarErrorBoundary>
      </body>
    </html>
  );
}
