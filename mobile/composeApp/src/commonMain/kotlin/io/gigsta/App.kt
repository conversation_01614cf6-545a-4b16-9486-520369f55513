package io.gigsta

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.lifecycle.viewmodel.compose.viewModel
import org.jetbrains.compose.ui.tooling.preview.Preview
import io.gigsta.di.AppModule
import io.gigsta.presentation.home.HomeScreen

@Composable
@Preview
fun App() {
    MaterialTheme {
        HomeScreen(
            viewModel = viewModel { AppModule.provideHomeViewModel() }
        )
    }
}