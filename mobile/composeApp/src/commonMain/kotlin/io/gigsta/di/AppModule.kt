package io.gigsta.di

import io.gigsta.data.repository.HistoryRepositoryImpl
import io.gigsta.domain.repository.HistoryRepository
import io.gigsta.domain.usecase.GetHistoryItemsUseCase
import io.gigsta.presentation.home.HomeViewModel

object AppModule {
    
    private val historyRepository: HistoryRepository by lazy {
        HistoryRepositoryImpl()
    }
    
    private val getHistoryItemsUseCase: GetHistoryItemsUseCase by lazy {
        GetHistoryItemsUseCase(historyRepository)
    }
    
    fun provideHomeViewModel(): HomeViewModel {
        return HomeViewModel(getHistoryItemsUseCase)
    }
}
