import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import { getResumeTemplateById } from '@/utils/resume-templates/resumeTemplates';
import { useAuth } from '@/hooks/useAuth';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

export interface ResumeHistoryItem {
  id: string;
  structuredData: any;
  htmlContent: string | null;
  templateId: string;
  templateName: string;
  createdAt: string;
  tokensDeducted: boolean;
}

export function useResumeHistory(auth: ReturnType<typeof useAuth>) {
  const { user, loading: authLoading } = auth;
  const [resumes, setResumes] = useState<ResumeHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const formatResume = (resume: any): ResumeHistoryItem => {
    const template = getResumeTemplateById(resume.template_id);

    return {
      id: resume.id,
      structuredData: resume.structured_data,
      htmlContent: resume.html_content,
      templateId: resume.template_id,
      templateName: template?.name || 'Clean Professional',
      createdAt: resume.created_at,
      tokensDeducted: resume.tokens_deducted || false
    };
  };

  const shouldIncludeResume = (resume: any): boolean => {
    return resume.status === 'done' &&
           resume.structured_data &&
           (resume.html_content || resume.structured_data);
  };

  const fetchResumes = async () => {
    if (!user) {
      setResumes([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      const { data: resumesData, error: fetchError } = await supabase
        .from('resumes')
        .select('id, structured_data, html_content, template_id, created_at, status, tokens_deducted')
        .eq('user_id', user.id)
        .eq('status', 'done')
        .not('structured_data', 'is', null)
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      const formattedResumes = resumesData?.map(formatResume) || [];
      setResumes(formattedResumes);
    } catch (err) {
      console.error('Error fetching resumes:', err);
      setError('Failed to fetch resumes');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!user) {
      setResumes([]);
      return;
    }

    // Initial fetch
    fetchResumes();

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel('resumes_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'resumes',
          filter: `user_id=eq.${user.id}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime resume change:', payload);

          if (payload.eventType === 'INSERT') {
            // Only add new resume if it meets our criteria
            if (shouldIncludeResume(payload.new)) {
              const newResume = formatResume(payload.new);
              setResumes(prev => [newResume, ...prev]);
            }
          } else if (payload.eventType === 'UPDATE') {
            // Handle update: either add, update, or remove based on criteria
            const meetsFilter = shouldIncludeResume(payload.new);
            const updatedResume = formatResume(payload.new);

            setResumes(prev => {
              const existingIndex = prev.findIndex(resume => resume.id === updatedResume.id);

              if (meetsFilter) {
                // Resume meets criteria
                if (existingIndex >= 0) {
                  // Update existing resume
                  return prev.map(resume =>
                    resume.id === updatedResume.id ? updatedResume : resume
                  );
                } else {
                  // Add new resume (it now meets criteria)
                  return [updatedResume, ...prev];
                }
              } else {
                // Resume no longer meets criteria, remove it if it exists
                return existingIndex >= 0
                  ? prev.filter(resume => resume.id !== updatedResume.id)
                  : prev;
              }
            });
          } else if (payload.eventType === 'DELETE') {
            // Remove deleted resume
            setResumes(prev =>
              prev.filter(resume => resume.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [user]);

  return {
    resumes,
    isLoading,
    error,
    refetch: fetchResumes
  };
}
