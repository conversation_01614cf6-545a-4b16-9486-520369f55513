-- Create trigger to automatically create a profile when a user account is created
-- This trigger will call the existing handle_new_user() function

CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Add comment for documentation
COMMENT ON TRIGGER on_auth_user_created ON auth.users IS 'Automatically creates a profile record when a new user account is created';
