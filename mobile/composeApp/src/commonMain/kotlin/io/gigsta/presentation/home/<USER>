package io.gigsta.presentation.home

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Send
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.model.HistoryItem
import io.gigsta.domain.model.HistoryItemType
import io.gigsta.domain.model.MenuItem
import io.gigsta.domain.usecase.GetHistoryItemsUseCase
import kotlinx.coroutines.launch

class HomeViewModel(
    private val getHistoryItemsUseCase: GetHistoryItemsUseCase
) : ViewModel() {
    
    var uiState by mutableStateOf(HomeUiState())
        private set
    
    val menuItems = listOf(
        MenuItem("cv_builder", "CV Builder", Icons.Default.Person, HistoryItemType.CV_BUILDER),
        MenuItem("application_letter", "Application Letter", Icons.Default.Email, HistoryItemType.APPLICATION_LETTER),
        MenuItem("email_application", "Email Application", Icons.Default.Send, HistoryItemType.EMAIL_APPLICATION),
        MenuItem("job_match", "Job Match", Icons.Default.Search, HistoryItemType.JOB_MATCH)
    )
    
    init {
        loadHistoryItems()
    }
    
    fun onTabSelected(index: Int) {
        uiState = uiState.copy(selectedTabIndex = index)
        loadHistoryItems()
    }
    
    fun onCreateNewItem() {
        val currentType = menuItems[uiState.selectedTabIndex].type
        // TODO: Navigate to create screen based on type
    }
    
    fun onHistoryItemClick(item: HistoryItem) {
        // TODO: Navigate to detail/edit screen
    }
    
    private fun loadHistoryItems() {
        val currentType = menuItems[uiState.selectedTabIndex].type
        
        viewModelScope.launch {
            uiState = uiState.copy(isLoading = true)
            try {
                val items = getHistoryItemsUseCase(currentType)
                uiState = uiState.copy(
                    historyItems = items,
                    isLoading = false,
                    error = null
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
}

data class HomeUiState(
    val selectedTabIndex: Int = 0,
    val historyItems: List<HistoryItem> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
