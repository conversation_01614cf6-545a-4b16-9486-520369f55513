# Gigsta - Local Development Setup

This guide will help you set up the Gigsta web application for local development.

## Prerequisites

Before you begin, make sure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **Git** for version control

## 1. Clone the Repository

```bash
git clone <repository-url>
cd GigstaProject/web
```

## 2. Install Dependencies

```bash
npm install
# or
yarn install
```

## 3. Supabase Setup

### Install Supabase CLI

```bash
npm install -g supabase
# or
brew install supabase/tap/supabase
```

### Initialize and Start Local Supabase

```bash
# Initialize Supabase in the project
supabase init

# Start local Supabase services
supabase start
```

This will start local Supabase services including:
- PostgreSQL database
- Authentication server
- Storage server
- Edge Functions runtime

### Run Database Migrations

The project includes pre-configured database migrations. Run them to set up your local database:

```bash
# Apply all migrations
supabase db reset

# Or if you want to apply migrations incrementally
supabase migration up
```

This will create all necessary tables, policies, and functions including:
- User profiles table
- Purchases/token system
- Storage buckets for resume uploads
- Row Level Security (RLS) policies
- Authentication triggers

### Get Local Supabase Keys

After starting Supabase locally, you'll see output similar to:

```
API URL: http://localhost:54321
GraphQL URL: http://localhost:54321/graphql/v1
DB URL: postgresql://postgres:postgres@localhost:54322/postgres
Studio URL: http://localhost:54323
Inbucket URL: http://localhost:54324
JWT secret: your-jwt-secret
anon key: your-anon-key
service_role key: your-service-role-key
```

Copy these values for your environment variables.

## 4. Environment Variables Setup

Create a `.env.local` file in the `web` directory with the following variables:

```bash
# Supabase Configuration (Local)
SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-local-service-role-key
```

> **Note**: Replace the Supabase keys with the actual values shown when you ran `supabase start`

## 5. Run the Application

```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:3000`.

## 6. Supabase Studio (Database Management)

You can access the local Supabase Studio at `http://localhost:54323` to:
- View and edit database tables
- Monitor authentication users
- Manage storage buckets
- Run SQL queries
- View logs and metrics

## Troubleshooting

### Common Issues

1. **Supabase Services Not Starting**
   - Make sure Docker is running
   - Try `supabase stop` then `supabase start`
   - Check if ports 54321-54324 are available

2. **Database Connection Errors**
   - Verify your local Supabase is running (`supabase status`)
   - Check if migrations were applied successfully
   - Ensure environment variables match the local Supabase output

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Review Supabase logs: `supabase logs`
3. Check Supabase Studio at `http://localhost:54323`
4. Verify all environment variables are set correctly
5. Ensure all required services are running: `supabase status`

## Project Structure

```
web/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── generate-application-letter/
│   │   ├── generate-email-application/
│   │   ├── generate-job-matching/
│   │   └── payment/              # Payment processing
│   ├── application-letter/       # Application letter page
│   ├── email-application/        # Email application page
│   ├── job-match/               # Job matching page
│   ├── auth/                    # Authentication pages
│   ├── buy-tokens/              # Token purchase
│   └── profile/                 # User profile
├── components/                   # Reusable UI components
├── hooks/                       # Custom React hooks
├── lib/                         # Utility libraries
│   └── supabase.ts             # Supabase client configuration
├── utils/                       # Utility functions
├── types/                       # TypeScript type definitions
└── public/                      # Static assets
```

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

---

**Happy coding!** 🚀
